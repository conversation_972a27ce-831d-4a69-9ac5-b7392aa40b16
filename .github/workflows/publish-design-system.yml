name: Publish Design System

on:
  workflow_dispatch:
    inputs:
      deploy_demo_site:
        description: 'Deploy demo site to GitHub Pages'
        required: false
        default: true
        type: boolean
      publish_package:
        description: 'Publish package to GitHub Package Registry'
        required: false
        default: true
        type: boolean
      dry_run:
        description: 'Run in dry-run mode (build only, no deploy/publish)'
        required: false
        default: false
        type: boolean

# Sets permissions for both GitHub Pages and Package Registry
permissions:
  contents: read
  pages: write
  id-token: write
  packages: write

# Allow only one concurrent deployment/publish
concurrency:
  group: "publish-design-system"
  cancel-in-progress: false

jobs:
  # Validate branch and setup
  setup:
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.package-version.outputs.version }}
      cache-key: ${{ steps.cache-key.outputs.key }}
      is-master: ${{ steps.branch-check.outputs.is-master }}
      
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Check if running on master branch
        id: branch-check
        run: |
          if [ "${{ github.ref }}" = "refs/heads/master" ]; then
            echo "is-master=true" >> $GITHUB_OUTPUT
            echo "✅ Running on master branch - deployment/publishing allowed"
          else
            echo "is-master=false" >> $GITHUB_OUTPUT
            echo "❌ Not on master branch (current: ${{ github.ref_name }}) - only dry-run allowed"
          fi

      - name: Fail if not master and not dry-run
        if: steps.branch-check.outputs.is-master == 'false' && inputs.dry_run != true
        run: |
          echo "ERROR: Deployment and publishing are only allowed from the master branch"
          echo "Current branch: ${{ github.ref_name }}"
          echo "Either:"
          echo "  1. Switch to master branch and run again"
          echo "  2. Enable 'dry-run mode' to test the build without deploying/publishing"
          exit 1
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          registry-url: 'https://npm.pkg.github.com'
          scope: '@psprowls'
          cache: 'npm'
          
      - name: Get package version
        id: package-version
        run: |
          VERSION=$(node -p "require('./package.json').version")
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "Package version: $VERSION"
          
      - name: Check if package version already exists
        if: inputs.publish_package == true && inputs.dry_run != true
        id: version-check
        run: |
          PACKAGE_NAME="@psprowls/design-system"
          VERSION="${{ steps.package-version.outputs.version }}"
          
          if npm view "$PACKAGE_NAME@$VERSION" --registry=https://npm.pkg.github.com 2>/dev/null; then
            echo "exists=true" >> $GITHUB_OUTPUT
            echo "❌ Version $VERSION already exists in the registry"
          else
            echo "exists=false" >> $GITHUB_OUTPUT
            echo "✅ Version $VERSION does not exist in the registry"
          fi
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Fail if package version exists
        if: steps.version-check.outputs.exists == 'true'
        run: |
          echo "ERROR: Version ${{ steps.package-version.outputs.version }} already exists in GitHub Package Registry"
          echo "Please update the version in package.json before publishing"
          echo "Run one of these commands:"
          echo "  npm run version:patch  # for bug fixes"
          echo "  npm run version:minor  # for new features"
          echo "  npm run version:major  # for breaking changes"
          exit 1

      - name: Generate cache key
        id: cache-key
        run: echo "key=design-system-${{ hashFiles('package-lock.json', 'src/**/*', 'stencil.config.ts') }}" >> $GITHUB_OUTPUT

      - name: Clear npm cache
        run: npm cache clean --force

      - name: Install dependencies
        run: |
          echo "Node.js version: $(node --version)"
          echo "npm version: $(npm --version)"
          rm -rf node_modules package-lock.json
          npm install
          echo "Verifying Stencil installation..."
          ls -la node_modules/@stencil/core/cli/
          ls -la node_modules/.bin/stencil
        
      - name: Cache dependencies
        uses: actions/cache/save@v4
        with:
          path: node_modules/
          key: ${{ steps.cache-key.outputs.key }}

  # Run tests in parallel
  test:
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Restore dependencies
        uses: actions/cache/restore@v4
        with:
          path: node_modules/
          key: ${{ needs.setup.outputs.cache-key }}
          fail-on-cache-miss: true

      - name: Install Chrome headless shell for Puppeteer
        run: |
          # Install Chrome headless shell that Puppeteer expects
          npx puppeteer browsers install chrome-headless-shell

          # Verify installation
          echo "Puppeteer Chrome headless shell installed"
          ls -la ~/.cache/puppeteer/ || echo "Cache directory not found"

      - name: Run tests
        run: npm test

  # Build core components (shared by both package and demo)
  build-core:
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Restore dependencies
        uses: actions/cache/restore@v4
        with:
          path: node_modules/
          key: ${{ needs.setup.outputs.cache-key }}
          fail-on-cache-miss: true
        
      - name: Build core components
        run: npm run build.src
        
      - name: Upload core build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: core-build
          path: dist/
          retention-days: 1

  # Build package (uses core build)
  build-package:
    runs-on: ubuntu-latest
    needs: [setup, build-core]
    if: inputs.publish_package == true
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Restore dependencies
        uses: actions/cache/restore@v4
        with:
          path: node_modules/
          key: ${{ needs.setup.outputs.cache-key }}
          fail-on-cache-miss: true

      - name: Download core build
        uses: actions/download-artifact@v4
        with:
          name: core-build
          path: dist/
        
      - name: Build React components
        run: npm run build.react
        
      - name: Verify package build
        run: |
          echo "Checking package build output..."
          ls -la dist/
          echo "Verifying required files exist..."
          test -f dist/index.js || (echo "ERROR: dist/index.js not found" && exit 1)
          test -f dist/index.cjs.js || (echo "ERROR: dist/index.cjs.js not found" && exit 1)
          test -d dist/types || (echo "ERROR: dist/types directory not found" && exit 1)
          test -d dist/react || (echo "ERROR: dist/react directory not found" && exit 1)
          echo "✅ All required package files are present"

      - name: Upload package artifacts
        uses: actions/upload-artifact@v4
        with:
          name: package-build
          path: dist/
          retention-days: 1

  # Build demo site (uses core build)
  build-demo:
    runs-on: ubuntu-latest
    needs: [setup, build-core]
    if: inputs.deploy_demo_site == true
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Restore dependencies
        uses: actions/cache/restore@v4
        with:
          path: node_modules/
          key: ${{ needs.setup.outputs.cache-key }}
          fail-on-cache-miss: true

      - name: Download core build
        uses: actions/download-artifact@v4
        with:
          name: core-build
          path: dist/
        
      - name: Build demo site
        run: npm run build.demo
        
      - name: Verify demo build
        run: |
          echo "Running comprehensive demo site verification..."
          chmod +x scripts/verify-build.sh
          ./scripts/verify-build.sh
          
      - name: Setup Pages
        if: needs.setup.outputs.is-master == 'true' && inputs.dry_run != true
        uses: actions/configure-pages@v5
        
      - name: Upload demo site artifact
        if: needs.setup.outputs.is-master == 'true' && inputs.dry_run != true
        uses: actions/upload-pages-artifact@v3
        with:
          path: './www'

      - name: Upload demo artifacts for inspection
        if: inputs.dry_run == true
        uses: actions/upload-artifact@v4
        with:
          name: demo-build
          path: www/
          retention-days: 1

  # Deploy demo site to GitHub Pages
  deploy-demo:
    runs-on: ubuntu-latest
    needs: [setup, test, build-demo]
    if: inputs.deploy_demo_site == true && needs.setup.outputs.is-master == 'true' && inputs.dry_run != true
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4

  # Publish package to GitHub Package Registry
  publish-package:
    runs-on: ubuntu-latest
    needs: [setup, test, build-package]
    if: inputs.publish_package == true && needs.setup.outputs.is-master == 'true' && inputs.dry_run != true
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          registry-url: 'https://npm.pkg.github.com'
          scope: '@psprowls'

      - name: Restore dependencies
        uses: actions/cache/restore@v4
        with:
          path: node_modules/
          key: ${{ needs.setup.outputs.cache-key }}
          fail-on-cache-miss: true

      - name: Download package artifacts
        uses: actions/download-artifact@v4
        with:
          name: package-build
          path: dist/

      - name: Publish to GitHub Package Registry
        run: |
          echo "INFO: Publishing version ${{ needs.setup.outputs.version }} to GitHub Package Registry"
          echo "INFO: Skipping prepublishOnly script since build artifacts are already prepared"
          npm publish --registry=https://npm.pkg.github.com --ignore-scripts
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # Create comprehensive summary
  summary:
    runs-on: ubuntu-latest
    needs: [setup, test, build-core, build-package, build-demo, deploy-demo, publish-package]
    if: always()
    steps:
      - name: Create deployment and publishing summary
        run: |
          echo "# 🚀 Design System Release Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Version:** ${{ needs.setup.outputs.version }}" >> $GITHUB_STEP_SUMMARY
          echo "**Branch:** ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
          echo "**Commit:** ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
          echo "**Dry Run:** ${{ inputs.dry_run }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          # Job status summary
          echo "## Job Status" >> $GITHUB_STEP_SUMMARY
          echo "| Job | Status |" >> $GITHUB_STEP_SUMMARY
          echo "|-----|--------|" >> $GITHUB_STEP_SUMMARY
          echo "| Setup | ${{ needs.setup.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Tests | ${{ needs.test.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Core Build | ${{ needs.build-core.result }} |" >> $GITHUB_STEP_SUMMARY

          if [ "${{ inputs.publish_package }}" = "true" ]; then
            echo "| Package Build | ${{ needs.build-package.result }} |" >> $GITHUB_STEP_SUMMARY
            if [ "${{ inputs.dry_run }}" != "true" ] && [ "${{ needs.setup.outputs.is-master }}" = "true" ]; then
              echo "| Package Publish | ${{ needs.publish-package.result }} |" >> $GITHUB_STEP_SUMMARY
            fi
          fi

          if [ "${{ inputs.deploy_demo_site }}" = "true" ]; then
            echo "| Demo Build | ${{ needs.build-demo.result }} |" >> $GITHUB_STEP_SUMMARY
            if [ "${{ inputs.dry_run }}" != "true" ] && [ "${{ needs.setup.outputs.is-master }}" = "true" ]; then
              echo "| Demo Deploy | ${{ needs.deploy-demo.result }} |" >> $GITHUB_STEP_SUMMARY
            fi
          fi

          echo "" >> $GITHUB_STEP_SUMMARY

          # What was accomplished
          echo "## What Was Accomplished" >> $GITHUB_STEP_SUMMARY

          if [ "${{ inputs.dry_run }}" = "true" ]; then
            echo "### 🧪 Dry Run Mode" >> $GITHUB_STEP_SUMMARY
            echo "- ✅ All builds completed successfully" >> $GITHUB_STEP_SUMMARY
            echo "- ✅ Tests passed" >> $GITHUB_STEP_SUMMARY
            echo "- ℹ️ No actual deployment or publishing performed" >> $GITHUB_STEP_SUMMARY
          else
            if [ "${{ needs.setup.outputs.is-master }}" = "true" ]; then
              if [ "${{ inputs.publish_package }}" = "true" ] && [ "${{ needs.publish-package.result }}" = "success" ]; then
                echo "### 📦 Package Published" >> $GITHUB_STEP_SUMMARY
                echo "- **Package:** @psprowls/design-system" >> $GITHUB_STEP_SUMMARY
                echo "- **Version:** ${{ needs.setup.outputs.version }}" >> $GITHUB_STEP_SUMMARY
                echo "- **Registry:** GitHub Package Registry" >> $GITHUB_STEP_SUMMARY
                echo "" >> $GITHUB_STEP_SUMMARY
                echo "**Installation:**" >> $GITHUB_STEP_SUMMARY
                echo '```bash' >> $GITHUB_STEP_SUMMARY
                echo "npm install @psprowls/design-system@${{ needs.setup.outputs.version }} --registry=https://npm.pkg.github.com" >> $GITHUB_STEP_SUMMARY
                echo '```' >> $GITHUB_STEP_SUMMARY
                echo "" >> $GITHUB_STEP_SUMMARY
              fi

              if [ "${{ inputs.deploy_demo_site }}" = "true" ] && [ "${{ needs.deploy-demo.result }}" = "success" ]; then
                echo "### 🌐 Demo Site Deployed" >> $GITHUB_STEP_SUMMARY
                echo "- **URL:** [View Demo Site](${{ needs.deploy-demo.outputs.page_url }})" >> $GITHUB_STEP_SUMMARY
                echo "- **Components:** All design system components" >> $GITHUB_STEP_SUMMARY
                echo "- **Documentation:** Component docs and examples" >> $GITHUB_STEP_SUMMARY
                echo "" >> $GITHUB_STEP_SUMMARY
              fi
            else
              echo "### ⚠️ Branch Restriction" >> $GITHUB_STEP_SUMMARY
              echo "- Builds completed successfully" >> $GITHUB_STEP_SUMMARY
              echo "- No deployment/publishing (not on master branch)" >> $GITHUB_STEP_SUMMARY
            fi
          fi

          echo "## Next Steps" >> $GITHUB_STEP_SUMMARY
          if [ "${{ inputs.dry_run }}" = "true" ]; then
            echo "- Run again without dry-run mode to actually deploy/publish" >> $GITHUB_STEP_SUMMARY
            echo "- Ensure you're on the master branch for deployment/publishing" >> $GITHUB_STEP_SUMMARY
          else
            if [ "${{ needs.publish-package.result }}" = "success" ]; then
              echo "- Update your projects to use the new package version" >> $GITHUB_STEP_SUMMARY
              echo "- Test integration in development environments" >> $GITHUB_STEP_SUMMARY
            fi
            if [ "${{ needs.deploy-demo.result }}" = "success" ]; then
              echo "- Share demo site with stakeholders for review" >> $GITHUB_STEP_SUMMARY
              echo "- Verify all components work correctly on the live site" >> $GITHUB_STEP_SUMMARY
            fi
          fi
