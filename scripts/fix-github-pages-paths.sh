#!/bin/bash

# GitHub Pages Path Fix Script
# This script fixes absolute paths in the built demo site to work with GitHub Pages subdirectory deployment

set -e  # Exit on any error

echo "🔧 Fixing paths for GitHub Pages deployment..."
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# GitHub Pages base path (repository name)
BASE_PATH="/design-system"

# Check if www directory exists
if [ ! -d "www" ]; then
    echo -e "${RED}ERROR: www directory not found. Run 'npm run build.demo' first.${NC}"
    exit 1
fi

echo "📁 Processing HTML files..."
echo "----------------------------"

# Fix HTML files - replace absolute paths with base path
find www -name "*.html" -type f | while read -r file; do
    echo "Processing: $file"
    
    # Fix script and link tags
    sed -i.bak \
        -e "s|src=\"/build/|src=\"${BASE_PATH}/build/|g" \
        -e "s|href=\"/build/|href=\"${BASE_PATH}/build/|g" \
        -e "s|href=\"/docs/|href=\"${BASE_PATH}/docs/|g" \
        -e "s|href=\"/assets/|href=\"${BASE_PATH}/assets/|g" \
        -e "s|href=\"/styles/|href=\"${BASE_PATH}/styles/|g" \
        -e "s|data-resources-url=\"/build/\"|data-resources-url=\"${BASE_PATH}/build/\"|g" \
        "$file"
    
    # Remove backup file
    rm "${file}.bak"
done

echo ""
echo "🎨 Processing CSS files..."
echo "---------------------------"

# Fix CSS files - replace absolute asset paths
find www -name "*.css" -type f | while read -r file; do
    echo "Processing: $file"
    
    # Fix font and asset URLs
    sed -i.bak \
        -e "s|url('/assets/|url('${BASE_PATH}/assets/|g" \
        -e "s|url(\"/assets/|url(\"${BASE_PATH}/assets/|g" \
        "$file"
    
    # Remove backup file
    rm "${file}.bak"
done

echo ""
echo "📄 Processing JavaScript files..."
echo "----------------------------------"

# Fix any hardcoded paths in JavaScript files (if any)
find www -name "*.js" -type f | while read -r file; do
    # Only process if file contains absolute paths that need fixing
    if grep -q '"/assets/' "$file" 2>/dev/null || grep -q '"/docs/' "$file" 2>/dev/null; then
        echo "Processing: $file"
        
        sed -i.bak \
            -e "s|\"/assets/|\"${BASE_PATH}/assets/|g" \
            -e "s|\"/docs/|\"${BASE_PATH}/docs/|g" \
            "$file"
        
        # Remove backup file
        rm "${file}.bak"
    fi
done

echo ""
echo "🔍 Verifying fixes..."
echo "----------------------"

# Count files that still have problematic absolute paths
# Check for specific patterns that should have been fixed
html_issues=$(find www -name "*.html" -exec grep -l 'src="/build/\|href="/build/\|href="/docs/\|href="/assets/\|href="/styles/' {} \; 2>/dev/null | wc -l)
css_issues=$(find www -name "*.css" -exec grep -l "url('/assets/\|url('/styles/" {} \; 2>/dev/null | wc -l)

if [ "$html_issues" -eq 0 ] && [ "$css_issues" -eq 0 ]; then
    echo -e "${GREEN}SUCCESS: All absolute paths have been fixed!${NC}"
    echo ""
    echo "✅ HTML files: Fixed script and link references"
    echo "✅ CSS files: Fixed asset URL references"
    echo "✅ JavaScript files: Fixed any hardcoded paths"
    echo ""
    echo "🚀 Ready for GitHub Pages deployment!"
    echo "   Site will be available at: https://psprowls.github.io${BASE_PATH}/"
else
    echo -e "${YELLOW}WARNING: Some absolute paths may still exist:${NC}"
    echo "   HTML files with issues: $html_issues"
    echo "   CSS files with issues: $css_issues"
    echo ""
    echo "Manual review recommended before deployment."
fi

echo ""
echo "📊 Summary"
echo "=========="
echo "Base path applied: ${BASE_PATH}"
echo "Files processed: $(find www -name "*.html" -o -name "*.css" -o -name "*.js" | wc -l)"
echo "Build directory: www/"
echo ""
